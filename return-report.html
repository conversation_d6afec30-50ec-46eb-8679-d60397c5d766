<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Return Report - Fun Land</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      overflow-x: hidden;
      background-color: #1c1c1c;
      color: #f8f9fa;
    }

    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
    }

    .sidebar a:hover {
      background-color: #444;
      color: #ff9900;
      padding-left: 25px;
    }

    .content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }

    .sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .content-expanded {
      margin-left: 0;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.22), 0 0 0 4px rgba(255, 204, 102, 0.10);
      border: 1.5px solid rgba(255, 204, 102, 0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .stats-card {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
      color: #fff;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .table-dark {
      background-color: rgba(34, 34, 34, 0.9);
      border-radius: 10px;
    }

    .table-dark th {
      background-color: #dc3545;
      color: #fff;
      border: none;
    }

    .table-dark td {
      border-color: #444;
      color: #f8f9fa;
    }

    .btn-danger {
      background-color: #dc3545;
      border-color: #dc3545;
      color: #fff;
    }

    .btn-danger:hover {
      background-color: #c82333;
      border-color: #bd2130;
      color: white;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
      }
      .sidebar.sidebar-collapsed {
        width: 0 !important;
      }
      .content {
        margin-left: 0 !important;
      }
    }
  </style>
</head>

<body>

  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html">Dashboard</a>
    <a href="#" onclick="showSearchCustomer()">Search Customer</a>
    <a href="#" onclick="showReports()">Reports</a>
    <a href="./add-user.html">Add Users</a>
    <a href="./packages.html">Packages</a>
    <a href="./setting.html">Settings</a>
    <a href="#" onclick="showReturn()">Return</a>
  </div>

  <div class="content" id="content">
    <div class="container-fluid">
      
      <!-- Return Report Section -->
      <div id="returnReportSection">
        <div class="glass-card">
          <h2 class="text-danger mb-4">
            <i class="bi bi-arrow-counterclockwise"></i> Return Report
          </h2>
          
          <!-- Stats Cards -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="totalReturns">PKR 0</h4>
                <p class="mb-0">Total Returns</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="todayReturns">PKR 0</h4>
                <p class="mb-0">Today's Returns</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="totalReturnTransactions">0</h4>
                <p class="mb-0">Total Transactions</p>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card text-center">
                <h4 id="avgReturn">PKR 0</h4>
                <p class="mb-0">Average Return</p>
              </div>
            </div>
          </div>

          <!-- Filter Section -->
          <div class="row mb-4">
            <div class="col-md-3">
              <label class="form-label text-danger">From Date</label>
              <input type="date" id="fromDate" class="form-control bg-dark text-light border-danger">
            </div>
            <div class="col-md-3">
              <label class="form-label text-danger">To Date</label>
              <input type="date" id="toDate" class="form-control bg-dark text-light border-danger">
            </div>
            <div class="col-md-3">
              <label class="form-label text-danger">Customer ID</label>
              <input type="text" id="customerFilter" class="form-control bg-dark text-light border-danger" placeholder="Enter Customer ID">
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button class="btn btn-danger w-100" onclick="filterReturns()">
                <i class="bi bi-funnel"></i> Filter
              </button>
            </div>
          </div>

          <!-- Returns Table -->
          <div class="table-responsive">
            <table class="table table-dark table-striped">
              <thead>
                <tr>
                  <th>Return ID</th>
                  <th>Date & Time</th>
                  <th>Customer ID</th>
                  <th>Customer Name</th>
                  <th>Return Amount</th>
                  <th>Reason</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody id="returnsTableBody">
                <!-- Return data will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Export Buttons -->
          <div class="row mt-4">
            <div class="col-md-6">
              <button class="btn btn-success w-100" onclick="exportToExcel()">
                <i class="bi bi-file-earmark-excel"></i> Export to Excel
              </button>
            </div>
            <div class="col-md-6">
              <button class="btn btn-info w-100" onclick="printReport()">
                <i class="bi bi-printer"></i> Print Report
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Return Processing Section -->
      <div id="returnSection" style="display:none;">
        <div class="glass-card mx-auto" style="max-width:500px;">
          <h2 class="text-danger mb-4 text-center">
            <i class="bi bi-arrow-down-circle"></i> Process Return
          </h2>
          
          <div class="mb-3" id="returnScanSection">
            <button class="btn btn-primary w-100" onclick="scanReturnCard()">
              <i class="bi bi-upc-scan"></i> Scan Customer Card
            </button>
          </div>

          <div id="returnForm" style="display:none;">
            <div class="row mb-3">
              <div class="col">
                <label class="form-label text-danger">Customer ID</label>
                <div class="fw-bold text-info" id="returnCustomerId"></div>
              </div>
              <div class="col">
                <label class="form-label text-danger">Customer Name</label>
                <div class="fw-bold text-info" id="returnCustomerName"></div>
              </div>
            </div>
            
            <div class="mb-3">
              <label class="form-label text-danger">Current Balance</label>
              <div class="fw-bold fs-4 text-success" id="returnCurrentBalance"></div>
            </div>

            <div class="mb-3">
              <label for="returnAmount" class="form-label text-danger">Return Amount</label>
              <input type="number" id="returnAmount" class="form-control bg-dark text-light border-danger" min="1" placeholder="Enter return amount">
            </div>

            <div class="mb-3">
              <label for="returnReason" class="form-label text-danger">Return Reason</label>
              <select id="returnReason" class="form-control bg-dark text-light border-danger">
                <option value="">Select reason</option>
                <option value="Game Issue">Game Issue</option>
                <option value="Customer Request">Customer Request</option>
                <option value="Technical Problem">Technical Problem</option>
                <option value="Refund">Refund</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <button class="btn btn-danger w-100 mt-3" onclick="processReturn()">
              <i class="bi bi-check-circle"></i> Process Return
            </button>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // Sample return data
    let returnData = [
      {
        id: 'RET001',
        date: '2024-01-15 14:30:00',
        customerId: 'CUST001',
        customerName: 'Ali Khan',
        amount: 150,
        reason: 'Game Issue',
        status: 'Completed'
      },
      {
        id: 'RET002', 
        date: '2024-01-15 15:45:00',
        customerId: 'CUST002',
        customerName: 'Sara Ahmed',
        amount: 80,
        reason: 'Customer Request',
        status: 'Completed'
      },
      {
        id: 'RET003',
        date: '2024-01-15 16:20:00', 
        customerId: 'CUST003',
        customerName: 'Hassan Ali',
        amount: 120,
        reason: 'Technical Problem',
        status: 'Completed'
      }
    ];

    let returnCustomer = null;

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    function showReturn() {
      document.getElementById('returnReportSection').style.display = 'none';
      document.getElementById('returnSection').style.display = 'block';
    }

    function showReports() {
      document.getElementById('returnSection').style.display = 'none';
      document.getElementById('returnReportSection').style.display = 'block';
      loadReturnReport();
    }

    function loadReturnReport() {
      updateStats();
      populateReturnsTable(returnData);
    }

    function updateStats() {
      const totalReturns = returnData.reduce((sum, ret) => sum + ret.amount, 0);
      const todaysReturns = returnData.filter(ret => 
        new Date(ret.date).toDateString() === new Date().toDateString()
      ).reduce((sum, ret) => sum + ret.amount, 0);
      
      document.getElementById('totalReturns').textContent = `PKR ${totalReturns}`;
      document.getElementById('todayReturns').textContent = `PKR ${todaysReturns}`;
      document.getElementById('totalReturnTransactions').textContent = returnData.length;
      document.getElementById('avgReturn').textContent = `PKR ${Math.round(totalReturns / returnData.length) || 0}`;
    }

    function populateReturnsTable(data) {
      const tbody = document.getElementById('returnsTableBody');
      tbody.innerHTML = '';
      
      data.forEach(ret => {
        const row = `
          <tr>
            <td>${ret.id}</td>
            <td>${new Date(ret.date).toLocaleString()}</td>
            <td>${ret.customerId}</td>
            <td>${ret.customerName}</td>
            <td>PKR ${ret.amount}</td>
            <td><span class="badge bg-warning">${ret.reason}</span></td>
            <td><span class="badge bg-success">${ret.status}</span></td>
          </tr>
        `;
        tbody.innerHTML += row;
      });
    }

    function scanReturnCard() {
      returnCustomer = {
        id: 'CUST' + Math.floor(Math.random() * 1000),
        name: 'Customer ' + Math.floor(Math.random() * 100),
        balance: Math.floor(Math.random() * 2000) + 500
      };

      document.getElementById('returnScanSection').style.display = 'none';
      document.getElementById('returnForm').style.display = 'block';
      document.getElementById('returnCustomerId').textContent = returnCustomer.id;
      document.getElementById('returnCustomerName').textContent = returnCustomer.name;
      document.getElementById('returnCurrentBalance').textContent = `PKR ${returnCustomer.balance}`;
    }

    function processReturn() {
      const amount = parseInt(document.getElementById('returnAmount').value);
      const reason = document.getElementById('returnReason').value;

      if (!amount || amount < 1) {
        alert('Please enter a valid return amount!');
        return;
      }

      if (amount > returnCustomer.balance) {
        alert('Return amount cannot be greater than current balance!');
        return;
      }

      if (!reason) {
        alert('Please select return reason!');
        return;
      }

      // Add new return to data
      const newReturn = {
        id: 'RET' + String(returnData.length + 1).padStart(3, '0'),
        date: new Date().toISOString(),
        customerId: returnCustomer.id,
        customerName: returnCustomer.name,
        amount: amount,
        reason: reason,
        status: 'Completed'
      };

      returnData.push(newReturn);

      // Update customer balance
      returnCustomer.balance -= amount;
      document.getElementById('returnCurrentBalance').textContent = `PKR ${returnCustomer.balance}`;

      // Clear form
      document.getElementById('returnAmount').value = '';
      document.getElementById('returnReason').value = '';

      alert(`Return processed successfully!\nReturn ID: ${newReturn.id}\nAmount: PKR ${amount}`);
    }

    function filterReturns() {
      // Filter functionality can be implemented here
      alert('Filter functionality will be implemented based on requirements');
    }

    function exportToExcel() {
      alert('Excel export functionality will be implemented');
    }

    function printReport() {
      window.print();
    }

    // Load return report by default
    window.onload = function() {
      loadReturnReport();
    };
  </script>

</body>
</html>
