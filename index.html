<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Responsive Sidebar</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      overflow-x: hidden;
      background-color: #1c1c1c;
      color: #f8f9fa;
    }

    .sidebar {
      height: 100vh;
      background-color: #222;
      padding-top: 60px;
      position: fixed;
      left: 0;
      top: 0;
      width: 250px;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar a {
      color: #ffcc66;
      padding: 15px;
      display: block;
      text-decoration: none;
    }

    .sidebar a:hover {
      background-color: #444;
      color: #ff9900;
    }

    .content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }

    .sidebar-collapsed {
      width: 0;
      overflow: hidden;
    }

    .content-expanded {
      margin-left: 0;
    }

    .toggle-btn {
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 1100;
      background-color: #ffcc66;
      border: none;
      color: #000;
    }

    .toggle-btn.active {
      background-color: #ff9900;
      color: white;
    }

    .main-sections {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 30px;
    }

    .left-section {
      flex: 1;
      background-color: #2c2c2c;
      padding: 20px;
      border-radius: 10px;
      position: relative;
      overflow: hidden;
    }

    .watermark-line {
      position: relative;
      width: 100%;
      overflow: hidden;
      height: 40px;
      margin-bottom: 10px;
    }

    .watermark-text {
      position: absolute;
      white-space: nowrap;
      font-size: 24px;
      color: #ffcc66;
      animation: slideText 10s linear infinite;
      opacity: 0.3;
    }

    @keyframes slideText {
      0% {
        left: 100%;
      }

      100% {
        left: -100%;
      }
    }

    .right-section {
      width: 300px;
      background-color: #333;
      padding: 20px;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
        padding-top: 60px;
      }

      .sidebar.sidebar-collapsed {
        width: 0 !important;
        padding: 0 !important;
      }

      .content {
        margin-left: 0 !important;
      }

      .content.content-expanded {
        margin-left: 0 !important;
      }

      .main-sections {
        flex-direction: column;
      }

      .right-section {
        width: 100%;
      }
    }

    .fade-in {
      animation: fadeIn 0.6s;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .box {
      /* background: rgba(40,40,40,0.7); */
      /* Remove or comment this line */
      backdrop-filter: blur(8px) brightness(1.1);
      border-radius: 18px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.18);
      border: 1.5px solid rgba(255, 204, 102, 0.15);
      transition:
        transform 0.35s cubic-bezier(.25, .8, .25, 1),
        box-shadow 0.35s cubic-bezier(.25, .8, .25, 1),
        background 0.35s,
        border 0.35s;
    }

    .box:hover {
      transform: scale(1.06);
      box-shadow:
        0 0 0 6px rgba(255, 204, 102, 0.10),
        0 16px 40px rgba(255, 204, 102, 0.25),
        0 2px 24px rgba(0, 0, 0, 0.18);
      background: rgba(255, 204, 102, 0.13);
      border: 1px solid #ffcc66;
      filter: drop-shadow(0 0 12px #ffcc66aa);
    }

    .sidebar a,
    .sidebar #reportsMenu {
      transition: background 0.3s, color 0.3s, padding-left 0.3s;
    }

    .sidebar a:hover,
    .sidebar #reportsMenu:hover {
      padding-left: 25px;
    }

    #reportsSubMenu {
      transition: max-height 0.4s, opacity 0.4s;
      overflow: hidden;
    }

    #reportsSubMenu[style*="display: block"] {
      opacity: 1;
      max-height: 200px;
    }

    #reportsSubMenu[style*="display: none"] {
      opacity: 0;
      max-height: 0;
    }

    #reportsSubMenu a {
      color: #ffcc66;
      padding: 10px 15px;
      display: block;
      text-decoration: none;
      font-size: 15px;
    }

    #reportsSubMenu a:hover {
      background-color: #444;
      color: #ff9900;
    }

    /* Modern glass card */
    .glass-card {
      background: rgba(34, 34, 34, 0.7);
      border-radius: 22px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.22), 0 0 0 4px rgba(255, 204, 102, 0.10);
      border: 1.5px solid rgba(255, 204, 102, 0.18);
      backdrop-filter: blur(14px) brightness(1.08);
      padding: 32px 28px 28px 28px;
      animation: fadeIn 0.7s;
      position: relative;
      overflow: hidden;
    }

    .card-icon-circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ffcc66 60%, #fffbe6 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18px;
      box-shadow: 0 2px 12px rgba(255, 204, 102, 0.15);
    }

    .glass-card .form-label {
      font-weight: 500;
      color: #ffcc66;
      margin-bottom: 4px;
    }

    .glass-card input.form-control {
      background: rgba(255, 255, 255, 0.07);
      border: 1px solid #ffcc66;
      color: #fff;
      border-radius: 8px;
    }

    .glass-card input.form-control:focus {
      border-color: #ff9900;
      background: rgba(255, 255, 255, 0.13);
      color: #fff;
    }

    .glass-card input.form-control::placeholder {
      color: #000 !important;
      opacity: 1;
    }

    .glass-card input::placeholder {
      color: #000 !important;
      opacity: 1;
    }

    .glass-card .btn {
      font-weight: 500;
      font-size: 1.08rem;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(255, 204, 102, 0.10);
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .glass-card .btn:hover {
      transform: translateY(-2px) scale(1.03);
      box-shadow: 0 8px 24px rgba(255, 204, 102, 0.18);
    }

    .glass-card .fw-bold {
      letter-spacing: 0.5px;
    }

    .glass-card .fs-4 {
      font-size: 1.5rem;
    }

    @media (max-width: 500px) {
      .glass-card {
        padding: 18px 8px 18px 8px;
      }
    }

    .box.bg-dark.text-warning,
    .box.bg-dark.text-info,
    .box.bg-dark.text-success,
    .box.bg-dark.text-danger {
      background: transparent !important;
      color: inherit;
    }

    /* Optional: Make box shadow more visible */
    .box {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.25);
    }
  </style>
</head>

<body>

  <button class="btn toggle-btn" id="toggleBtn" onclick="toggleSidebar()">☰</button>

  <div class="sidebar" id="sidebar">
    <a href="./index.html" onclick="changeTab('Dashboard')">Dashboard</a>
    <a href="#" onclick="changeTab('Search Customer')">Search Customer</a>
    <a href="#" id="reportsMenu" onclick="toggleReportsMenu()">Reports ▼</a>
    <div id="reportsSubMenu" style="display:none; margin-left:20px;">
      <a href="./sale-report.html">Sale Report</a>
      <a href="#" onclick="changeTab('Sale Written')">Written</a>
      <a href="#" onclick="changeTab('Individual')">Individual</a>
    </div>
    <a href="./add-user.html" onclick="changeTab('Add Users')">Add Users</a>
    <a href="./packages.html" onclick="changeTab('Add Users')">packages</a>
    <a href="./setting.html" onclick="changeTab('Settings')">Settings</a>
  </div>

  <div class="content" id="content">
    <div class="container-fluid">

      <!-- TOP BOXES START -->
      <div class="row mb-4" style="gap:20px;">
        <div class="col box bg-dark text-warning p-3 rounded" style="min-width:200px;">
          <h5>Amount</h5>
          <div>Total Issued: <span id="amountIssued">0</span></div>
          <div>Total Used: <span id="amountUsed">0</span></div>
        </div>
        <div class="col box bg-dark text-info p-3 rounded" style="min-width:200px;">
          <h5>Promotion Amount</h5>
          <div>Total Issued: <span id="promoIssued">0</span></div>
          <div>Total Used: <span id="promoUsed">0</span></div>
        </div>
        <div class="col box bg-dark text-success p-3 rounded" style="min-width:200px;">
          <h5>Deal Amount</h5>
          <div>Total Issued: <span id="dealIssued">0</span></div>
          <div>Total Used: <span id="dealUsed">0</span></div>
        </div>
        <div class="col box bg-dark text-danger p-3 rounded" style="min-width:200px;">
          <h5>Return</h5>
          <div>Total Issued: <span id="returnIssued">0</span></div>
          <div>Total Used: <span id="returnUsed">0</span></div>
        </div>
      </div>
      <!-- TOP BOXES END -->

      <div class="main-sections">
        <div class="left-section" id="mainContent">
          <h4>Main Content</h4>
          <p>This is the main larger section where you can place your primary content.</p>
        </div>
        <div class="right-section">
          <button class="btn btn-warning text-dark" onclick="changeTab('Tab 1 Content')">Top Up</button>
          <button class="btn btn-warning text-dark" onclick="changeTab('Tab 2 Content')">Return</button>
          <button class="btn btn-warning text-dark" onclick="changeTab('Tab 3 Content')">Add Customer</button>
        </div>
      </div>

    </div>
  </div>

  <div id="idleOverlay"
    style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; z-index:2000; backdrop-filter:blur(8px); background:rgba(30,30,30,0.5);">
    <div id="funLandAnim" style="position:absolute; font-size:48px; color:#ffcc66; opacity:0.7; white-space:nowrap;">FUN
      LAND • FUN LAND • FUN LAND • FUN LAND •</div>
  </div>

  <!-- Password Modal -->
  <div id="passwordModal"
    style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; z-index:2000; backdrop-filter:blur(8px); background:rgba(30,30,30,0.5);">
    <div
      style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); background:#333; padding:30px; border-radius:15px; box-shadow:0 8px 32px rgba(0,0,0,0.3); border:1px solid #ffcc66;">
      <h4 style="color:#ffcc66; text-align:center; margin-bottom:20px;">Enter Promotion Password</h4>
      <input type="password" id="promotionPassword"
        style="width:100%; padding:10px; border:1px solid #ffcc66; border-radius:8px; background:rgba(255,255,255,0.1); color:#fff; margin-bottom:20px;"
        placeholder="Enter password">
      <div style="display:flex; gap:10px;">
        <button onclick="checkPromotionPassword()"
          style="flex:1; padding:10px; background:#28a745; color:white; border:none; border-radius:8px; cursor:pointer;">Submit</button>
        <button onclick="closePasswordModal()"
          style="flex:1; padding:10px; background:#dc3545; color:white; border:none; border-radius:8px; cursor:pointer;">Cancel</button>
      </div>
    </div>
  </div>

  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const content = document.getElementById('content');
      const toggleBtn = document.getElementById('toggleBtn');

      sidebar.classList.toggle('sidebar-collapsed');
      content.classList.toggle('content-expanded');
      toggleBtn.classList.toggle('active');

      // Toggle icon
      toggleBtn.innerText = toggleBtn.innerText === '☰' ? '✖' : '☰';
    }

    let topupUser = null;
    let returnUser = null;
    let newCustomerId = null;
    let promoUser = null;

    function showScanMessage() {
      const mainContent = document.getElementById('mainContent');
      mainContent.innerHTML = `
        <h2 class="text-center mt-5">Scan your card</h2>
        <p class="text-center">Please scan your card to continue.</p>
        <div class="d-flex justify-content-center mt-4">
          <button class="btn btn-primary" onclick="scanCard()">Simulate Scan</button>
        </div>
      `;
    }

    // Simulate card scan (replace with real logic)
    function scanCard() {
      user = {
        id: 'U12345',
        name: 'Ali Khan',
        balance: 500
      };
      changeTab('Tab 1 Content'); // Show Top Up tab after scan
    }

    // Update changeTab to check user
    function changeTab(contentText) {
      const mainContent = document.getElementById('mainContent');

      // TOP UP TAB
      if (contentText === 'Tab 1 Content') {
        mainContent.innerHTML = `
          <div class="glass-card mx-auto" style="max-width:410px;">
            <div class="card-icon-circle mx-auto mb-3">
              <i class="bi bi-cash-coin fs-2 text-dark"></i>
            </div>
            <h3 class="mb-3 text-warning text-center">Top Up</h3>
            <div class="mb-3" id="topupOptions">
              <button class="btn btn-warning w-100 mb-2" onclick="showNormalTopUp()">
                <i class="bi bi-cash"></i> Normal Top Up
              </button>
              <button class="btn btn-success w-100" onclick="showPromotionTopUp()">
                <i class="bi bi-gift"></i> Promotion Top Up
              </button>
            </div>
            
            <!-- Normal Top Up Section -->
            <div id="normalTopUpSection" style="display:none;">
              <div class="mb-3" id="topupScanSection">
                <button class="btn btn-primary w-100" onclick="scanTopUpCard()">
                  <i class="bi bi-upc-scan"></i> Scan Card for Top Up
                </button>
              </div>
              <div id="topupForm" style="display:none;">
                <div class="row mb-3">
                  <div class="col">
                    <label class="form-label">User ID</label>
                    <div class="fw-bold text-info" id="topupUserId"></div>
                  </div>
                  <div class="col">
                    <label class="form-label">User Name</label>
                    <div class="fw-bold text-info" id="topupUserName"></div>
                  </div>
                </div>
                <div class="mb-3">
                  <label for="topupAmount" class="form-label">Top Up Amount</label>
                  <input type="number" id="topupAmount" class="form-control" min="1" placeholder="Enter amount">
                </div>
                <div class="mb-3 d-flex align-items-center gap-2">
                  <label class="form-label mb-0">Current Balance:</label>
                  <div class="fw-bold fs-4 text-success mb-0" id="topupCurrentBalance"></div>
                </div>
                <button class="btn btn-warning w-100 mt-2" onclick="doTopUp()">
                  <i class="bi bi-plus-circle"></i> Add Cash
                </button>
              </div>
              <button class="btn btn-secondary w-100 mt-2" onclick="backToTopUpOptions()">Back</button>
            </div>
            
            <!-- Promotion Top Up Section -->
            <div id="promotionTopUpSection" style="display:none;">
              <div class="mb-3" id="promoScanSection">
                <button class="btn btn-primary w-100" onclick="scanPromoCard()">
                  <i class="bi bi-upc-scan"></i> Scan Card for Promotion
                </button>
              </div>
              <div id="promoForm" style="display:none;">
                <div class="row mb-3">
                  <div class="col">
                    <label class="form-label">User ID</label>
                    <div class="fw-bold text-info" id="promoUserId"></div>
                  </div>
                  <div class="col">
                    <label class="form-label">User Name</label>
                    <div class="fw-bold text-info" id="promoUserName"></div>
                  </div>
                </div>
                <div class="mb-3">
                  <label for="promoAmount" class="form-label">Promotion Amount</label>
                  <input type="number" id="promoAmount" class="form-control" min="1" placeholder="Enter promotion amount">
                </div>
                <div class="mb-3 d-flex align-items-center gap-2">
                  <label class="form-label mb-0">Current Balance:</label>
                  <div class="fw-bold fs-4 text-success mb-0" id="promoCurrentBalance"></div>
                </div>
                <button class="btn btn-success w-100 mt-2" onclick="doPromotion()">
                  <i class="bi bi-gift"></i> Add Promotion
                </button>
              </div>
              <button class="btn btn-secondary w-100 mt-2" onclick="backToTopUpOptions()">Back</button>
            </div>
          </div>
        `;
      }
      // RETURN TAB
      else if (contentText === 'Tab 2 Content') {
        mainContent.innerHTML = `
          <div class="glass-card mx-auto" style="max-width:410px;">
            <div class="card-icon-circle mx-auto mb-3">
              <i class="bi bi-arrow-counterclockwise fs-2 text-dark"></i>
            </div>
            <h3 class="mb-3 text-danger text-center">Return</h3>
            <div class="mb-3" id="returnScanSection">
              <button class="btn btn-primary w-100" onclick="scanReturnCard()">
                <i class="bi bi-upc-scan"></i> Scan Card for Return
              </button>
            </div>
            <div id="returnForm" style="display:none;">
              <!-- User Info Row -->
              <div class="row mb-3">
                <div class="col">
                  <label class="form-label">User ID</label>
                  <div class="fw-bold text-info" id="returnUserId"></div>
                </div>
                <div class="col">
                  <label class="form-label">User Name</label>
                  <div class="fw-bold text-info" id="returnUserName"></div>
                </div>
              </div>
              <div class="mb-3">
                <label for="returnAmount" class="form-label">Return Amount</label>
                <input type="number" id="returnAmount" class="form-control" min="1" placeholder="Enter amount">
              </div>
              <div class="mb-3 d-flex align-items-center gap-2">
                <label class="form-label mb-0">Current Balance:</label>
                <div class="fw-bold fs-4 text-success mb-0" id="returnCurrentBalance"></div>
              </div>
              <button class="btn btn-danger w-100 mt-2" onclick="doReturn()">
                <i class="bi bi-arrow-down-circle"></i> Return Cash
              </button>
            </div>
          </div>
        `;
      }
      // ADD CUSTOMER TAB
      else if (contentText === 'Tab 3 Content') {
        newCustomerId = null;
        mainContent.innerHTML = `
          <div class="glass-card mx-auto" style="max-width:410px;">
            <div class="card-icon-circle mx-auto mb-3">
              <i class="bi bi-person-plus fs-2 text-dark"></i>
            </div>
            <h3 class="mb-3 text-primary text-center">Add Customer</h3>
            <div class="mb-3" id="scanSection">
              <button class="btn btn-primary w-100" onclick="scanNewCustomerCard()">
                <i class="bi bi-upc-scan"></i> Scan your card
              </button>
            </div>
            <div id="customerForm" style="display:none;">
              <div class="mb-3">
                <label class="form-label">Card ID</label>
                <div class="fw-bold text-info" id="newCardId"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Name</label>
                <input type="text" id="custName" class="form-control" placeholder="Enter name">
              </div>
              <div class="mb-3">
                <label class="form-label">Phone Number</label>
                <input type="text" id="custPhone" class="form-control" placeholder="Enter phone number">
              </div>
              <button class="btn btn-success w-100 mt-2" onclick="addCustumer()">
                <i class="bi bi-person-check"></i> Add Customer
              </button>
            </div>
          </div>
        `;
      }








    }

    // Helper functions - moved outside changeTab to make them globally accessible
    function showReport(type) {
      const reportContent = document.getElementById('reportContent');
      if (type === 'Sale Written') {
        reportContent.innerHTML = `<h5>Sale Written Report</h5><p>Show sale written data here.</p>`;
      } else if (type === 'Individual') {
        reportContent.innerHTML = `<h5>Individual Report</h5><p>Show individual data here.</p>`;
      }
    }

    function scanNewCustomerCard() {
      // Simulate scan, real logic yahan lagayen
      newCustomerId = 'CUST' + Math.floor(Math.random() * 10000);
      document.getElementById('scanSection').style.display = 'none';
      document.getElementById('customerForm').style.display = 'block';
      document.getElementById('newCardId').innerText = newCustomerId;
    }

    function scanTopUpCard() {
      // Simulate different card scan
      topupUser = {
        id: 'TU' + Math.floor(Math.random() * 10000),
        name: 'Top Up User',
        balance: Math.floor(Math.random() * 1000) + 100
      };

      document.getElementById('topupScanSection').style.display = 'none';
      document.getElementById('topupForm').style.display = 'block';
      document.getElementById('topupUserId').innerText = topupUser.id;
      document.getElementById('topupUserName').innerText = topupUser.name;
      document.getElementById('topupCurrentBalance').innerText = topupUser.balance;
    }

    function scanReturnCard() {
      // Simulate different card scan
      returnUser = {
        id: 'RU' + Math.floor(Math.random() * 10000),
        name: 'Return User',
        balance: Math.floor(Math.random() * 1000) + 200
      };

      document.getElementById('returnScanSection').style.display = 'none';
      document.getElementById('returnForm').style.display = 'block';
      document.getElementById('returnUserId').innerText = returnUser.id;
      document.getElementById('returnUserName').innerText = returnUser.name;
      document.getElementById('returnCurrentBalance').innerText = returnUser.balance;
    }

    function doTopUp() {
      const amountInput = document.getElementById('topupAmount');
      const balanceElem = document.getElementById('topupCurrentBalance');
      let amount = parseInt(amountInput.value, 10);
      if (!amount || amount < 1) {
        alert('Enter a valid amount!');
        return;
      }
      const oldBalance = topupUser.balance;
      topupUser.balance += amount;
      animateBalance(oldBalance, topupUser.balance, balanceElem);
      amountInput.value = '';
    }

    function doReturn() {
      const amountInput = document.getElementById('returnAmount');
      const balanceElem = document.getElementById('returnCurrentBalance');
      let amount = parseInt(amountInput.value, 10);
      if (!amount || amount < 1) {
        alert('Enter a valid amount!');
        return;
      }
      if (amount > returnUser.balance) {
        alert('Return amount cannot be greater than current balance!');
        return;
      }
      const oldBalance = returnUser.balance;
      returnUser.balance -= amount;
      animateBalance(oldBalance, returnUser.balance, balanceElem);
      amountInput.value = '';
    }

    function animateBalance(from, to, element) {
      const duration = 800;
      const start = performance.now();

      function update(now) {
        const progress = Math.min((now - start) / duration, 1);
        const value = Math.floor(from + (to - from) * progress);
        element.innerText = value;
        if (progress < 1) {
          requestAnimationFrame(update);
        } else {
          element.innerText = to; // Final value
        }
      }
      requestAnimationFrame(update);
    }

    function addCustumer() {
      const name = document.getElementById('custName').value.trim();
      const phone = document.getElementById('custPhone').value.trim();
      if (!newCustomerId || !name || !phone) {
        alert('Please scan card and fill all fields!');
        return;
      }
      alert(`Customer Added!\nCard: ${newCustomerId}\nName: ${name}\nPhone: ${phone}`);
    }

    function scanPromoCard() {
      promoUser = {
        id: 'PR' + Math.floor(Math.random() * 10000),
        name: 'Promo User',
        balance: Math.floor(Math.random() * 1000) + 150
      };

      document.getElementById('promoScanSection').style.display = 'none';
      document.getElementById('promoForm').style.display = 'block';
      document.getElementById('promoUserId').innerText = promoUser.id;
      document.getElementById('promoUserName').innerText = promoUser.name;
      document.getElementById('promoCurrentBalance').innerText = promoUser.balance;
    }

    function doPromotion() {
      const amountInput = document.getElementById('promoAmount');
      const balanceElem = document.getElementById('promoCurrentBalance');
      let amount = parseInt(amountInput.value, 10);
      if (!amount || amount < 1) {
        alert('Enter a valid promotion amount!');
        return;
      }
      const oldBalance = promoUser.balance;
      promoUser.balance += amount;
      animateBalance(oldBalance, promoUser.balance, balanceElem);
      amountInput.value = '';
    }

    function showNormalTopUp() {
      document.getElementById('topupOptions').style.display = 'none';
      document.getElementById('normalTopUpSection').style.display = 'block';
    }

    function showPromotionTopUp() {
      document.getElementById('passwordModal').style.display = 'block';
      document.getElementById('promotionPassword').focus();
    }

    function checkPromotionPassword() {
      const password = document.getElementById('promotionPassword').value;
      if (password === 'admin123') { // Change this to your desired password
        closePasswordModal();
        document.getElementById('topupOptions').style.display = 'none';
        document.getElementById('promotionTopUpSection').style.display = 'block';
      } else {
        alert('Incorrect password!');
        document.getElementById('promotionPassword').value = '';
      }
    }

    function closePasswordModal() {
      document.getElementById('passwordModal').style.display = 'none';
      document.getElementById('promotionPassword').value = '';
    }

    function backToTopUpOptions() {
      document.getElementById('normalTopUpSection').style.display = 'none';
      document.getElementById('promotionTopUpSection').style.display = 'none';
      document.getElementById('topupOptions').style.display = 'block';
    }

    function toggleReportsMenu() {
      const subMenu = document.getElementById('reportsSubMenu');
      const reportsMenu = document.getElementById('reportsMenu');

      if (subMenu.style.display === 'none' || subMenu.style.display === '') {
        subMenu.style.display = 'block';
        reportsMenu.innerHTML = 'Reports ▲';
      } else {
        subMenu.style.display = 'none';
        reportsMenu.innerHTML = 'Reports ▼';
      }
    }

    // Close modal on Enter key
    document.addEventListener('keydown', function (e) {
      if (e.key === 'Enter' && document.getElementById('passwordModal').style.display === 'block') {
        checkPromotionPassword();
      }
    });

    // Idle animation functions
    let idleTimeout;
    let overlay = document.getElementById('idleOverlay');
    let animText = document.getElementById('funLandAnim');
    let animX = 0, animY = 0, dx = 2, dy = 1;

    function showIdleOverlay() {
      overlay.style.display = 'block';
      animX = 0; animY = 0;
      animateFunLand();
    }

    function hideIdleOverlay() {
      overlay.style.display = 'none';
      cancelAnimationFrame(animFrame);
    }

    function resetIdleTimer() {
      hideIdleOverlay();
      clearTimeout(idleTimeout);
      idleTimeout = setTimeout(showIdleOverlay, 5000);
    }

    let animFrame;
    function animateFunLand() {
      animX += dx;
      animY += dy;
      if (animX + animText.offsetWidth > window.innerWidth || animX < 0) dx = -dx;
      if (animY + animText.offsetHeight > window.innerHeight || animY < 0) dy = -dy;
      animText.style.left = animX + 'px';
      animText.style.top = animY + 'px';
      animFrame = requestAnimationFrame(animateFunLand);
    }

    ['mousemove', 'keydown', 'mousedown', 'touchstart'].forEach(evt => {
      window.addEventListener(evt, resetIdleTimer);
    });

    resetIdleTimer();

    // Load Top Up by default when page loads
    window.onload = function () {
      changeTab('Tab 1 Content');
    };

  </script>

</body>

</html>
